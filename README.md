# Indian Market Trading Bot

An automated trading system for the Indian stock market that processes Telegram signals and executes trades through SmartAPI. Single Python file with built-in configuration management.

## 🚀 Quick Start

### 1. Installation
```bash
git clone <repository-url>
cd tele_client
pip install -r requirements.txt
```

### 2. Configuration
Edit `trading_config.py` with your credentials:
```python
# Telegram Configuration
API_ID = "your_telegram_api_id"
API_HASH = "your_telegram_api_hash"
GROUP_NAME = "your_telegram_group_name"

# SmartAPI Configuration
SMARTAPI_API_KEY = "your_smartapi_key"
SMARTAPI_USER_ID = "your_user_id"
SMARTAPI_PIN = "your_pin"
SMARTAPI_TOTP_SECRET = "your_totp_secret"

# Trading Settings
DEFAULT_LOT_SIZE = 1
ENABLE_ACTUAL_TRADING = False  # Keep False for testing
```

### 3. Usage Commands

**Start Trading Bot:**
```bash
python main.py                    # Start in current mode
```

**Configuration Management:**
```bash
python main.py --config show      # Show current settings
python main.py --config sim       # Enable simulation mode
python main.py --config live      # Enable live trading (with warnings)
python main.py --lot-size 2       # Update lot size to 2
```

## 📊 Signal Formats

The bot processes 5 signal formats from Telegram:

1. **BUY**: `===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CE$32.4$30.5$2025-09-16 10:15:00+05:30`
2. **HOLD**: `===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 CE...`
3. **CLOSE**: `===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CE$35.2$2025-09-16 14:30:00+05:30$profit_exit`
4. **STOP LOSS**: `===Algo_Trading===$Update$STOP LOSS to$32.0$for option$NIFTY 16 SEP 25000 CE...`
5. **CROSSOVER**: `===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CE$28.5$2025-09-16 15:00:00+05:30$opposite_crossover_exit`

## 🛠️ How to Use

### Step 1: Setup
1. Install Python dependencies: `pip install -r requirements.txt`
2. Edit `trading_config.py` with your Telegram and SmartAPI credentials
3. Ensure you're a member of the Telegram group specified in config

### Step 2: Test First (Important!)
```bash
# Check current configuration
python main.py --config show

# Start in simulation mode (safe - no real trades)
python main.py
```

### Step 3: Go Live (After Testing)
```bash
# Enable live trading mode
python main.py --config live

# Start live trading
python main.py
```

## 📋 Configuration Commands

| Command | Description |
|---------|-------------|
| `python main.py` | Start trading bot |
| `python main.py --config show` | Show current settings |
| `python main.py --config sim` | Enable simulation mode |
| `python main.py --config live` | Enable live trading |
| `python main.py --lot-size 2` | Set lot size to 2 |

## 📊 What It Does

- **Listens** to Telegram group for trading signals
- **Processes** 5 different signal formats (BUY, HOLD, CLOSE, STOP LOSS, CROSSOVER)
- **Executes** trades through SmartAPI during market hours (9:15 AM - 3:30 PM)
- **Manages** positions with automatic stop loss and timeout handling
- **Logs** all activities to CSV files for tracking

## 🔧 Key Settings

Edit these in `trading_config.py`:

```python
# Essential Settings
DEFAULT_LOT_SIZE = 1              # Number of lots per trade
ENABLE_ACTUAL_TRADING = False     # True for live trading
SIGNAL_TIMEOUT_SECONDS = 30       # Auto-close timeout
MAX_POSITIONS = 5                 # Maximum open positions

# Your Credentials
API_ID = "your_telegram_api_id"
API_HASH = "your_telegram_api_hash"
GROUP_NAME = "your_telegram_group"
SMARTAPI_API_KEY = "your_smartapi_key"
SMARTAPI_USER_ID = "your_user_id"
SMARTAPI_PIN = "your_pin"
SMARTAPI_TOTP_SECRET = "your_totp_secret"
```

## 📁 Output Files

- `logs/trading_YYYY-MM-DD.log` - Daily activity logs
- `trade_logs/trades_YYYY-MM-DD.csv` - Trade execution records
- `trade_logs/signals_YYYY-MM-DD.csv` - All received signals

## ⚠️ Safety Features

- **Simulation Mode**: Test without real money
- **Market Hours Only**: Trades only during market hours
- **Auto-Close**: Closes positions if no signal for 30 seconds
- **Position Limits**: Prevents over-trading
- **Emergency Shutdown**: Closes all positions on exit

## 🚨 Important

1. **Always test in simulation mode first**
2. **Never share your API credentials**
3. **Monitor logs regularly**
4. **Start with small lot sizes**
5. **Understand the risks involved**
