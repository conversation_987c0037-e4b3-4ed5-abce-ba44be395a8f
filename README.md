# Enhanced Algo Trading System

A comprehensive, production-ready algorithmic trading system for the Indian stock market with Telegram signal processing and SmartAPI integration.

## 🚀 Features

### Core Functionality
- **Real-time Signal Processing**: Processes 5 different signal formats from Telegram
- **Comprehensive Order Management**: Handles BUY, SELL, CLOSE, STOP LOSS updates, and HOLD signals
- **Risk Management**: Configurable lot sizes, position limits, and auto-close functionality
- **Robust Error Handling**: Retry mechanisms, fallback procedures, and graceful degradation
- **Audit Trail**: Complete CSV logging of all signals and trades

### Signal Formats Supported
1. **Format1 (BUY)**: `===Algo_Trading===$TRADE$BUY$SYMBOL DD MMM STRIKE PUT/CALL$PRICE$STOP_LOSS$TIMESTAMP`
2. **Format2 (HOLD)**: `===Algo_Trading===$INTIMATION$Continue to hold trade for $SYMBOL...`
3. **Format3 (CLOSE)**: `===Algo_Trading===$TRADE$CLOSE$SYMBOL DD MMM STRIKE PUT/CALL$PRICE$TIMESTAMP$REASON`
4. **Format4 (STOP LOSS UPDATE)**: `===Algo_Trading===$Update$STOP LOSS to$PRICE$for option$SYMBOL...`
5. **Format5 (CROSSOVER EXIT)**: `===Algo_Trading===$TRADE$CLOSE$SYMBOL...$opposite_crossover_exit`

### Safety Features
- **Market Hours Detection**: Only trades during market hours (9:15 AM - 3:30 PM IST)
- **Position Timeout**: Auto-closes positions if no signal received for 30 seconds
- **Emergency Shutdown**: Force closes all positions on system shutdown
- **Simulation Mode**: Test signals without actual trading

## 📁 Project Structure

```
tele_client/
├── main.py                     # 🎯 Main application (single entry point)
├── trading_config.py           # 🆕 Centralized configuration
├── trading_utils.py            # 🆕 Management utilities
├── config_manager.py           # 🆕 Configuration management tool
├── requirements.txt            # Minimal dependencies
├── utils/                      # 🔄 Renamed from utils_new/
│   ├── api_handler.py         # SmartAPI integration & order management
│   ├── signal_handler.py      # Telegram signal processing
│   ├── logger.py              # Optimized logging system
│   ├── market_data_manager.py # Market data handling
│   ├── trading_dataframe_manager.py # Trade tracking
│   └── instrument_updater.py  # Instrument file management
├── Dependencies/              # Instrument files
├── logs/                     # Daily log files
├── trade_logs/              # CSV trade and signal logs
└── tests/                   # Test suite
```

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd tele_client
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure the system**:
   Edit `trading_config.py` with your credentials:
   ```python
   # Telegram Configuration
   API_ID = "your_telegram_api_id"
   API_HASH = "your_telegram_api_hash"
   GROUP_NAME = "your_telegram_group_name"
   
   # SmartAPI Configuration
   SMARTAPI_API_KEY = "your_smartapi_key"
   SMARTAPI_USER_ID = "your_user_id"
   SMARTAPI_PIN = "your_pin"
   SMARTAPI_TOTP_SECRET = "your_totp_secret"
   
   # Trading Parameters
   DEFAULT_LOT_SIZE = 1
   ENABLE_ACTUAL_TRADING = False  # Set to True for live trading
   ```

## 🚦 Usage

### Starting the Bot

**Simulation Mode** (recommended for testing):
```bash
python main.py
```

**Live Trading Mode**:
```bash
# Enable live trading (with safety warnings)
python config_manager.py live

# Start the bot
python main.py
```

### Configuration Management

Use the new `config_manager.py` tool for easy configuration:

```bash
# Show current configuration
python config_manager.py show

# Enable simulation mode (safe testing)
python config_manager.py sim

# Enable live trading (with warnings)
python config_manager.py live

# Update lot size
python config_manager.py lot-size --size 2
```

### Configuration Options

Key parameters in `trading_config.py`:

| Parameter | Description | Default |
|-----------|-------------|---------|
| `DEFAULT_LOT_SIZE` | Number of lots per trade | 1 |
| `SIGNAL_TIMEOUT_SECONDS` | Auto-close timeout | 30 |
| `ENABLE_ACTUAL_TRADING` | Enable live trading | False |
| `MAX_POSITIONS` | Maximum concurrent positions | 5 |
| `ENABLE_AUTO_CLOSE` | Auto-close on timeout | True |

### Environment Variables

For production deployment, use environment variables:
```bash
export TELEGRAM_API_ID="your_api_id"
export SMARTAPI_API_KEY="your_api_key"
export ENABLE_ACTUAL_TRADING="true"
export DEFAULT_LOT_SIZE="2"
```

## 📊 Monitoring & Logs

### Log Files
- **Daily Logs**: `logs/trading_YYYY-MM-DD.log`
- **Trade CSV**: `trade_logs/trades_YYYY-MM-DD.csv`
- **Signal CSV**: `trade_logs/signals_YYYY-MM-DD.csv`

### System Status
The bot logs system status every 5 minutes:
```
💓 System Status - Market: OPEN, Positions: 2
```

### Trade Confirmations
All trades are logged with details:
```
TRADE: ORDER PLACED: BUY 75 units of NIFTY16SEP202525000PE at 32.4
```

## 🔧 Advanced Features

### Position Management
- **Active Positions**: View all open positions
- **Auto-Close**: Positions closed if no signal for 30 seconds
- **Emergency Close**: Force close all positions on shutdown

### Error Handling
- **API Retry Logic**: Automatic retry on API failures
- **Fallback Mechanisms**: Graceful degradation when services unavailable
- **Rate Limit Handling**: Automatic delays on rate limit errors

### Risk Controls
- **Market Hours**: Only trades during market hours
- **Position Limits**: Configurable maximum positions
- **Stop Loss**: Automatic stop loss management

## 🚨 Important Notes

1. **Test First**: Always test in simulation mode before live trading
2. **Monitor Logs**: Keep an eye on log files for any issues
3. **Market Hours**: System only trades during Indian market hours
4. **Backup**: Keep backups of your configuration and logs
5. **Updates**: Regularly update instrument files for accurate trading

## 🔒 Security

- Never commit credentials to version control
- Use environment variables for production
- Regularly rotate API keys and secrets
- Monitor trade logs for unauthorized activity

## 📞 Support

For issues or questions:
1. Check the log files for error messages
2. Verify your configuration settings
3. Ensure all dependencies are installed
4. Test with simulation mode first

## 📄 License

This project is for educational and personal use only. Use at your own risk.
