# Indian Market Trading Bot

Automated trading system for the Indian stock market that processes Telegram signals and executes trades through SmartAPI. Features configurable lot sizes, comprehensive risk management, and single-file operation. Uses uv for fast, reliable Python environment management.

## 🚀 Quick Start

### 1. Installation

**Prerequisites:**
- Python 3.8+ (uv will manage the exact version)
- uv package manager

**Setup:**
```bash
git clone <repository-url>
cd tele_client

# Install uv if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install dependencies and create virtual environment
uv sync
```

**Why uv?**
- ⚡ **10-100x faster** than pip for dependency resolution
- 🔒 **Reliable** dependency locking and reproducible builds
- 🎯 **Automatic** virtual environment management
- 📦 **Single tool** for all Python package management needs

**Alternative (Traditional pip):**
```bash
# If you prefer using pip instead of uv
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt

# Then use regular python commands instead of 'uv run python'
python main.py --config show
```

> **Note**: This project uses `requirements.txt` for dependency management, which works seamlessly with both uv and pip. uv automatically creates and manages virtual environments, making dependency management faster and more reliable.

### 2. Configuration
Edit `trading_config.py` with your credentials:
```python
# Telegram Configuration
API_ID = "your_telegram_api_id"
API_HASH = "your_telegram_api_hash"
GROUP_NAME = "your_telegram_group_name"

# SmartAPI Configuration
SMARTAPI_API_KEY = "your_smartapi_key"
SMARTAPI_USER_ID = "your_user_id"
SMARTAPI_PIN = "your_pin"
SMARTAPI_TOTP_SECRET = "your_totp_secret"

# Trading Settings
DEFAULT_LOT_SIZE = 1
ENABLE_ACTUAL_TRADING = False  # Keep False for testing
```

### 3. Basic Usage

**Start Trading Bot:**
```bash
uv run python main.py                    # Start in current mode
```

**Configuration Management:**
```bash
uv run python main.py --config show      # Show current settings
uv run python main.py --config sim       # Enable simulation mode
uv run python main.py --config live      # Enable live trading (with warnings)
uv run python main.py --lot-size 2       # Update default lot size to 2
```

## 📊 Signal Formats

The bot processes 5 signal formats from Telegram:

1. **BUY**: `===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CE$32.4$30.5$2025-09-16 10:15:00+05:30`
2. **HOLD**: `===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 CE...`
3. **CLOSE**: `===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CE$35.2$2025-09-16 14:30:00+05:30$profit_exit`
4. **STOP LOSS**: `===Algo_Trading===$Update$STOP LOSS to$32.0$for option$NIFTY 16 SEP 25000 CE...`
5. **CROSSOVER**: `===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CE$28.5$2025-09-16 15:00:00+05:30$opposite_crossover_exit`

## 🛠️ How to Use

### Step 1: Setup
1. Install dependencies using uv: `uv sync`
2. Edit `trading_config.py` with your Telegram and SmartAPI credentials
3. Ensure you're a member of the Telegram group specified in config

### Step 2: Test First (Important!)
```bash
# Check current configuration
uv run python main.py --config show

# Start in simulation mode (safe - no real trades)
uv run python main.py
```

### Step 3: Go Live (After Testing)
```bash
# Enable live trading mode
uv run python main.py --config live

# Start live trading
uv run python main.py
```

## 📋 All Commands

### Basic Operations
| Command | Description |
|---------|-------------|
| `uv run python main.py` | Start trading bot |
| `uv run python main.py --config show` | Show current settings |
| `uv run python main.py --config sim` | Enable simulation mode |
| `uv run python main.py --config live` | Enable live trading |

### Lot Size Management
| Command | Description |
|---------|-------------|
| `uv run python main.py --lot-size 2` | Set default lot size to 2 |
| `uv run python main.py --list-lot-sizes` | List all custom lot sizes |
| `uv run python main.py --add-lot-size NIFTY_25000_CE_16SEP --size 2` | Add custom lot size |
| `uv run python main.py --remove-lot-size NIFTY_25000_CE_16SEP` | Remove custom lot size |

## 📊 What It Does

- **Listens** to Telegram group for trading signals
- **Processes** 5 different signal formats (BUY, HOLD, CLOSE, STOP LOSS, CROSSOVER)
- **Executes** trades through SmartAPI during market hours (9:15 AM - 3:30 PM)
- **Manages** positions with automatic stop loss and timeout handling
- **Supports** configurable lot sizes for different option chains
- **Logs** all activities to CSV files for tracking and analysis

## 🔧 Key Settings

Edit these in `trading_config.py`:

```python
# Essential Settings
DEFAULT_LOT_SIZE = 1              # Number of lots per trade
ENABLE_ACTUAL_TRADING = False     # True for live trading
SIGNAL_TIMEOUT_SECONDS = 30       # Auto-close timeout
MAX_POSITIONS = 5                 # Maximum open positions

# Your Credentials
API_ID = "your_telegram_api_id"
API_HASH = "your_telegram_api_hash"
GROUP_NAME = "your_telegram_group"
SMARTAPI_API_KEY = "your_smartapi_key"
SMARTAPI_USER_ID = "your_user_id"
SMARTAPI_PIN = "your_pin"
SMARTAPI_TOTP_SECRET = "your_totp_secret"
```

## 🎯 Custom Lot Sizes

The bot supports configurable lot sizes for different option chains, allowing you to set different lot sizes for different trading strategies.

### How It Works
- **Default Behavior**: Uses `DEFAULT_LOT_SIZE = 1` for all unconfigured option chains
- **Custom Override**: Specific option chains can have custom lot sizes
- **Format**: `SYMBOL_STRIKE_OPTIONTYPE_EXPIRY` (e.g., `NIFTY_25000_CE_16SEP`)
- **Matching**: Case-insensitive matching for flexibility
- **Persistence**: Changes are saved to configuration file and persist across restarts

### Managing Custom Lot Sizes

**Add Custom Lot Size:**
```bash
uv run python main.py --add-lot-size NIFTY_25000_CE_16SEP --size 2
uv run python main.py --add-lot-size BANKNIFTY_45000_PE_18SEP --size 3
```

**List All Custom Lot Sizes:**
```bash
uv run python main.py --list-lot-sizes
```

**Remove Custom Lot Size:**
```bash
uv run python main.py --remove-lot-size NIFTY_25000_CE_16SEP
```

### Example Usage
```bash
# Set up different lot sizes for different strategies
uv run python main.py --add-lot-size NIFTY_25000_CE_16SEP --size 2    # Conservative
uv run python main.py --add-lot-size BANKNIFTY_45000_PE_18SEP --size 3 # Aggressive

# Check current configuration
uv run python main.py --list-lot-sizes

# Start trading with custom lot sizes
uv run python main.py
```

## 📁 Output Files

The bot generates comprehensive logs for monitoring and analysis:

- `logs/trading_YYYY-MM-DD.log` - Daily activity logs with detailed events
- `trade_logs/trades_YYYY-MM-DD.csv` - Trade execution records with lot sizes
- `trade_logs/signals_YYYY-MM-DD.csv` - All received signals for audit trail

## ⚠️ Safety Features

- **Simulation Mode**: Test strategies without real money
- **Market Hours Only**: Trades only during Indian market hours (9:15 AM - 3:30 PM)
- **Auto-Close**: Automatically closes positions if no signal received for 30 seconds
- **Position Limits**: Configurable maximum concurrent positions (default: 5)
- **Emergency Shutdown**: Force closes all positions on system exit
- **Risk Management**: Configurable daily loss limits and stop loss management

## 🚨 Important Guidelines

1. **Always test in simulation mode first** - Verify your configuration works correctly
2. **Never share your API credentials** - Keep your SmartAPI and Telegram credentials secure
3. **Monitor logs regularly** - Check daily logs for any issues or unexpected behavior
4. **Start with small lot sizes** - Begin with conservative lot sizes and increase gradually
5. **Understand the risks** - Options trading involves significant financial risk
6. **Keep backups** - Backup your configuration and important trade logs
7. **Stay updated** - Regularly update instrument files for accurate trading

## 🔧 Troubleshooting

**Bot not receiving signals?**
- Verify you're a member of the correct Telegram group
- Check GROUP_NAME in trading_config.py matches exactly
- Ensure Telegram API credentials are correct

**Trades not executing?**
- Check if market is open (9:15 AM - 3:30 PM IST, weekdays only)
- Verify SmartAPI credentials and account status
- Check if ENABLE_ACTUAL_TRADING is set to True for live trading

**Custom lot sizes not working?**
- Restart the bot after adding/removing custom lot sizes
- Verify option chain format: SYMBOL_STRIKE_OPTIONTYPE_EXPIRY
- Check configuration with `uv run python main.py --config show`
